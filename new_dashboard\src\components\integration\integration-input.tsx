import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/utils';

interface IntegrationInputProps {
   id: string;
   name: string;
   label: string;
   type?: string;
   placeholder?: string;
   value: string;
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   required?: boolean;
   disabled?: boolean;
   error?: string;
   className?: string;
}

const IntegrationInput: React.FC<IntegrationInputProps> = ({
   id,
   name,
   label,
   type = 'text',
   placeholder,
   value,
   onChange,
   required = false,
   disabled = false,
   error,
   className,
}) => {
   return (
      <div className={cn('space-y-2', className)}>
         <Label 
            htmlFor={id}
            className="text-sm font-medium text-gray-700 dark:text-gray-300"
         >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
         </Label>
         <Input
            id={id}
            name={name}
            type={type}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            required={required}
            disabled={disabled}
            className={cn(
               'h-11 transition-colors',
               error && 'border-red-500 focus-visible:ring-red-500',
               disabled && 'opacity-50 cursor-not-allowed'
            )}
         />
         {error && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">
               {error}
            </p>
         )}
      </div>
   );
};

export default IntegrationInput;
