import React, { ReactNode } from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import StepIndicator from '@/components/ui/step-indicator';
import { cn } from '@/utils';

interface IntegrationWrapperProps {
   title: string;
   logo: string;
   logoAlt?: string;
   steps: string[];
   currentStep?: number;
   completedSteps?: number[];
   children: ReactNode;
   className?: string;
   onBack?: () => void;
   showBackButton?: boolean;
}

const IntegrationWrapper: React.FC<IntegrationWrapperProps> = ({
   title,
   logo,
   logoAlt = 'Integration Logo',
   steps,
   currentStep = 0,
   completedSteps = [],
   children,
   className,
   onBack,
   showBackButton = true,
}) => {
   const navigate = useNavigate();

   const handleBack = () => {
      if (onBack) {
         onBack();
      } else {
         navigate(-1);
      }
   };

   return (
      <div className={cn('min-h-screen bg-gray-50 dark:bg-gray-900', className)}>
         <div className="flex h-screen">
            {/* Left Panel - Steps */}
            <div className="w-1/2 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-8 overflow-y-auto">
               {/* Header */}
               <div className="mb-8">
                  {showBackButton && (
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleBack}
                        className="mb-6 -ml-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
                     >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                     </Button>
                  )}
                  <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                     Integration Steps
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400">
                     Follow these steps to connect your {title} account
                  </p>
               </div>

               {/* Step Indicator */}
               <StepIndicator
                  steps={steps}
                  currentStep={currentStep}
                  completedSteps={completedSteps}
               />
            </div>

            {/* Right Panel - Form */}
            <div className="w-1/2 bg-white dark:bg-gray-800 flex flex-col">
               {/* Logo Header */}
               <div className="border-b border-gray-200 dark:border-gray-700 p-8">
                  <div className="flex items-center justify-center">
                     <img
                        src={logo}
                        alt={logoAlt}
                        className="h-16 w-auto object-contain"
                     />
                  </div>
               </div>

               {/* Form Content */}
               <div className="flex-1 p-8 overflow-y-auto">
                  <div className="max-w-md mx-auto">
                     {children}
                  </div>
               </div>
            </div>
         </div>
      </div>
   );
};

export default IntegrationWrapper;
