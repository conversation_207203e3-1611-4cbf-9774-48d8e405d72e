import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

interface UseIntegrationProps<T> {
   initialFormData: T;
   validateCredentials: (data: T) => Promise<{ valid: boolean; message: string }>;
   connectIntegration: (data: T & { client_id: string }) => Promise<void>;
   encryptCredentials?: (data: Partial<T>) => Promise<Partial<T>>;
   redirectPath?: string;
   integrationName: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

export function useIntegration<T extends Record<string, any>>({
   initialFormData,
   validateCredentials,
   connectIntegration,
   encryptCredentials,
   redirectPath = '/social-listening',
   integrationName,
}: UseIntegrationProps<T>) {
   const navigate = useNavigate();
   const [formData, setFormData] = useState<T>(initialFormData);
   const [isLoading, setIsLoading] = useState(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({
         ...prev,
         [name]: value,
      }));
   }, []);

   const handleSubmit = useCallback(
      async (e: React.FormEvent<HTMLFormElement>, client_id: string) => {
         e.preventDefault();
         if (!client_id) return;

         try {
            setIsLoading(true);
            setApiError(null);

            // Validate credentials
            const validationResult = await validateCredentials(formData);
            if (!validationResult.valid) {
               throw new Error(validationResult.message);
            }

            // Encrypt credentials if needed
            let processedData = formData;
            if (encryptCredentials) {
               const encryptedData = await encryptCredentials(formData);
               processedData = { ...formData, ...encryptedData };
            }

            // Connect integration
            await connectIntegration({ ...processedData, client_id });

            // Success
            setApiError({
               success: true,
               message: `${integrationName} connected successfully! Redirecting...`,
            });

            // Reset form
            setFormData(initialFormData);

            // Redirect after delay
            setTimeout(() => {
               navigate(redirectPath);
            }, 2000);

         } catch (error) {
            console.error(`Error connecting to ${integrationName}:`, error);
            setApiError({
               success: false,
               message: error instanceof Error ? error.message : `Failed to connect to ${integrationName}. Please check your credentials.`,
            });
         } finally {
            setIsLoading(false);
         }
      },
      [formData, initialFormData, validateCredentials, encryptCredentials, connectIntegration, integrationName, navigate, redirectPath]
   );

   const resetForm = useCallback(() => {
      setFormData(initialFormData);
      setApiError(null);
   }, [initialFormData]);

   return {
      formData,
      isLoading,
      apiError,
      handleInputChange,
      handleSubmit,
      resetForm,
      setFormData,
      setApiError,
   };
}
