# Integration Components

This directory contains modular, reusable components for creating integration forms with consistent UI/UX across all integrations.

## Components Overview

### 1. IntegrationWrapper
The main layout wrapper that provides the two-panel design with step indicator and form area.

### 2. IntegrationForm
A standardized form component with built-in error/success handling and loading states.

### 3. IntegrationInput
A consistent input component with proper styling and validation display.

### 4. IntegrationTemplate
A complete template component that combines all the above for quick integration creation.

### 5. StepIndicator
A beautiful vertical step indicator with green checkmarks and dotted lines.

## Quick Start

### Option 1: Using IntegrationTemplate (Recommended for new integrations)

```tsx
import { IntegrationTemplate } from '@/components/integration';
import myServiceLogo from '../images/my-service-logo.png';

const MyServiceForm = () => {
  const steps = [
    'Log in to your MyService account',
    'Go to Settings → API Keys',
    'Generate a new API key',
    'Copy the API key and secret',
    'Enter credentials below'
  ];

  const formFields = [
    {
      id: 'api<PERSON><PERSON>',
      name: 'api<PERSON><PERSON>',
      label: 'API Key',
      placeholder: 'Enter your API key...',
      required: true
    },
    {
      id: 'apiSecret',
      name: 'apiSecret',
      label: 'API Secret',
      type: 'password',
      placeholder: 'Enter your API secret...',
      required: true
    }
  ];

  const validateCredentials = async (data) => {
    // Your validation logic
    const response = await fetch('/api/validate-myservice', {
      method: 'POST',
      body: JSON.stringify(data)
    });
    return response.json();
  };

  const connectIntegration = async (data) => {
    // Your connection logic
    await fetch('/api/connect-myservice', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  };

  return (
    <IntegrationTemplate
      integrationName="MyService"
      logo={myServiceLogo}
      steps={steps}
      formFields={formFields}
      validateCredentials={validateCredentials}
      connectIntegration={connectIntegration}
      title="MyService Integration"
      description="Connect your MyService account to sync data"
    />
  );
};
```

### Option 2: Using Individual Components (For custom layouts)

```tsx
import { 
  IntegrationWrapper, 
  IntegrationForm, 
  IntegrationInput 
} from '@/components/integration';

const CustomIntegrationForm = () => {
  // Your state and handlers
  
  return (
    <IntegrationWrapper
      title="Custom Integration"
      logo={logo}
      steps={steps}
      currentStep={currentStep}
      completedSteps={completedSteps}
    >
      <IntegrationForm
        title="Connect Account"
        description="Enter your credentials"
        onSubmit={handleSubmit}
        isLoading={isLoading}
        error={error}
        success={success}
      >
        <IntegrationInput
          id="username"
          name="username"
          label="Username"
          value={formData.username}
          onChange={handleChange}
          required
        />
        {/* Add more inputs as needed */}
      </IntegrationForm>
    </IntegrationWrapper>
  );
};
```

## Features

### ✅ Modern UI/UX
- Clean, professional design using shadcn/ui components
- Consistent styling across all integrations
- Dark mode support
- Responsive layout

### ✅ Step Indicator
- Vertical timeline with green checkmarks
- Dotted connecting lines
- Current step highlighting
- Completed step tracking

### ✅ Form Handling
- Built-in loading states
- Error and success message display
- Form validation
- Consistent input styling

### ✅ Modular Architecture
- Reusable components
- Easy to extend and customize
- Type-safe with TypeScript
- Consistent API across integrations

### ✅ Integration Hook
- `useIntegration` hook for common integration logic
- Handles form state, validation, and API calls
- Consistent error handling
- Automatic redirects

## Migration Guide

To migrate existing integrations to use these new components:

1. **Replace the layout**: Replace your existing layout components with `IntegrationWrapper`
2. **Update the form**: Use `IntegrationForm` instead of custom form components
3. **Replace inputs**: Use `IntegrationInput` for consistent styling
4. **Use the hook**: Optionally use `useIntegration` hook for common logic

## Styling

The components use Tailwind CSS classes and are designed to work with your existing design system. All components support:

- Custom className props for additional styling
- Dark mode variants
- Responsive design
- Consistent spacing and typography

## Best Practices

1. **Use IntegrationTemplate** for new integrations unless you need custom layout
2. **Keep step descriptions clear** and actionable
3. **Provide helpful placeholder text** for inputs
4. **Handle errors gracefully** with user-friendly messages
5. **Test with both light and dark themes**
6. **Ensure responsive design** works on all screen sizes
