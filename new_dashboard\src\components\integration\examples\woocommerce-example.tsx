import React from 'react';
import { IntegrationTemplate } from '@/components/integration';
// import woocommerceLogo from '../images/woocommerce-logo.png';

/**
 * Example: WooCommerce Integration using IntegrationTemplate
 * 
 * This shows how easy it is to create a new integration using the template.
 * Just define your steps, form fields, and API functions!
 */

const WooCommerceExampleForm = () => {
   // Define the integration steps
   const steps = [
      'Log in to your WooCommerce admin dashboard',
      'Navigate to WooCommerce → Settings → Advanced → REST API',
      'Click "Add Key" to create a new API key',
      'Set Description as "Flable Integration"',
      'Set User as your admin user',
      'Set Permissions to "Read/Write"',
      'Click "Generate API Key"',
      'Copy the Consumer Key and Consumer Secret',
      'Enter the credentials below along with your store URL'
   ];

   // Define form fields
   const formFields = [
      {
         id: 'storeUrl',
         name: 'storeUrl',
         label: 'Store URL',
         placeholder: 'https://yourstore.com',
         required: true
      },
      {
         id: 'consumerKey',
         name: 'consumerKey',
         label: 'Consumer Key',
         placeholder: 'ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
         required: true
      },
      {
         id: 'consumerSecret',
         name: 'consumerSecret',
         label: 'Consumer Secret',
         type: 'password',
         placeholder: 'cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
         required: true
      }
   ];

   // API function to validate credentials
   const validateCredentials = async (data: any) => {
      try {
         const response = await fetch('/api/validate-woocommerce', {
            method: 'POST',
            headers: {
               'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
         });
         
         const result = await response.json();
         return {
            valid: result.valid,
            message: result.message || 'Credentials validated successfully'
         };
      } catch (error) {
         return {
            valid: false,
            message: 'Failed to validate credentials'
         };
      }
   };

   // API function to connect the integration
   const connectIntegration = async (data: any) => {
      const response = await fetch('/api/connect-woocommerce', {
         method: 'POST',
         headers: {
            'Content-Type': 'application/json',
         },
         body: JSON.stringify(data),
      });

      if (!response.ok) {
         throw new Error('Failed to connect WooCommerce integration');
      }
   };

   // Optional: Function to encrypt sensitive data
   const encryptCredentials = async (data: any) => {
      if (data.consumerSecret) {
         const response = await fetch('/api/encrypt', {
            method: 'POST',
            headers: {
               'Content-Type': 'application/json',
            },
            body: JSON.stringify({ data: data.consumerSecret }),
         });
         
         const result = await response.json();
         return {
            ...data,
            consumerSecret: result.encrypted
         };
      }
      return data;
   };

   return (
      <IntegrationTemplate
         integrationName="WooCommerce"
         logo="/images/woocommerce-logo.png" // Replace with actual logo import
         steps={steps}
         formFields={formFields}
         validateCredentials={validateCredentials}
         connectIntegration={connectIntegration}
         encryptCredentials={encryptCredentials}
         title="WooCommerce Store"
         description="Connect your WooCommerce store to sync products, orders, and customer data."
         redirectPath="/integrations"
         currentStep={0}
         completedSteps={[]}
      />
   );
};

export default WooCommerceExampleForm;

/**
 * Usage in your routing:
 * 
 * ```tsx
 * import WooCommerceExampleForm from '@/components/integration/examples/woocommerce-example';
 * 
 * // In your routes
 * <Route path="/integrations/woocommerce" element={<WooCommerceExampleForm />} />
 * ```
 * 
 * That's it! You now have a fully functional, beautiful integration form with:
 * - Modern UI with step indicator
 * - Form validation and error handling
 * - Loading states
 * - Success/error messages
 * - Consistent styling
 * - Dark mode support
 * - Responsive design
 */
