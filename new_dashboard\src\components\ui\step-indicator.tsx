import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/utils';

interface StepIndicatorProps {
   steps: string[];
   currentStep?: number;
   completedSteps?: number[];
   className?: string;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
   steps,
   currentStep = 0,
   completedSteps = [],
   className,
}) => {
   return (
      <div className={cn('relative', className)}>
         {/* Vertical line */}
         <div className="absolute left-4 top-8 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700" />
         
         {/* Steps */}
         <div className="space-y-6">
            {steps.map((step, index) => {
               const isCompleted = completedSteps.includes(index);
               const isCurrent = index === currentStep;
               const isUpcoming = index > currentStep && !isCompleted;
               
               return (
                  <div key={index} className="relative flex items-start">
                     {/* Step indicator circle */}
                     <div className="relative z-10 flex items-center justify-center">
                        <div
                           className={cn(
                              'flex h-8 w-8 items-center justify-center rounded-full border-2 transition-all duration-200',
                              {
                                 'bg-green-500 border-green-500 text-white': isCompleted,
                                 'bg-blue-500 border-blue-500 text-white': isCurrent,
                                 'bg-white border-gray-300 text-gray-400 dark:bg-gray-800 dark:border-gray-600': isUpcoming,
                              }
                           )}
                        >
                           {isCompleted ? (
                              <Check className="h-4 w-4" />
                           ) : (
                              <span className="text-sm font-medium">{index + 1}</span>
                           )}
                        </div>
                     </div>
                     
                     {/* Step content */}
                     <div className="ml-4 flex-1 pb-6">
                        <div
                           className={cn(
                              'text-sm font-medium transition-colors duration-200',
                              {
                                 'text-green-600 dark:text-green-400': isCompleted,
                                 'text-blue-600 dark:text-blue-400': isCurrent,
                                 'text-gray-500 dark:text-gray-400': isUpcoming,
                              }
                           )}
                        >
                           Step {index + 1}
                        </div>
                        <div
                           className={cn(
                              'mt-1 text-sm leading-relaxed transition-colors duration-200',
                              {
                                 'text-gray-700 dark:text-gray-300': isCompleted || isCurrent,
                                 'text-gray-500 dark:text-gray-400': isUpcoming,
                              }
                           )}
                           dangerouslySetInnerHTML={{
                              __html: step.replace(/\n/g, '<br />'),
                           }}
                        />
                     </div>
                  </div>
               );
            })}
         </div>
      </div>
   );
};

export default StepIndicator;
