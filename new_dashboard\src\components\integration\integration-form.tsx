import React, { ReactNode } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/utils';

interface IntegrationFormProps {
   title: string;
   description: string;
   children: ReactNode;
   onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
   isLoading?: boolean;
   error?: string | null;
   success?: string | null;
   submitButtonText?: string;
   className?: string;
}

const IntegrationForm: React.FC<IntegrationFormProps> = ({
   title,
   description,
   children,
   onSubmit,
   isLoading = false,
   error,
   success,
   submitButtonText = 'Connect',
   className,
}) => {
   return (
      <Card className={cn('w-full shadow-lg border-0', className)}>
         <CardHeader className="text-center pb-6">
            <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
               {title}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 mt-2">
               {description}
            </CardDescription>
         </CardHeader>
         
         <CardContent>
            {/* Status Messages */}
            {error && (
               <Alert className="mb-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
                  <AlertDescription className="text-red-700 dark:text-red-400">
                     {error}
                  </AlertDescription>
               </Alert>
            )}
            
            {success && (
               <Alert className="mb-6 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20">
                  <AlertDescription className="text-green-700 dark:text-green-400">
                     {success}
                  </AlertDescription>
               </Alert>
            )}

            {/* Form */}
            <form onSubmit={onSubmit} className="space-y-6">
               {children}
               
               <Button
                  type="submit"
                  className="w-full h-11 text-base font-medium"
                  disabled={isLoading}
               >
                  {isLoading ? (
                     <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Connecting...
                     </>
                  ) : (
                     submitButtonText
                  )}
               </Button>
            </form>
         </CardContent>
      </Card>
   );
};

export default IntegrationForm;
