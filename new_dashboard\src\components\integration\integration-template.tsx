import React from 'react';
import { IntegrationWrapper, IntegrationForm, IntegrationInput } from '@/components/integration';
import { useIntegration } from '@/hooks/use-integration';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';

interface IntegrationTemplateProps {
   // Integration specific props
   integrationName: string;
   logo: string;
   steps: string[];
   
   // Form configuration
   formFields: Array<{
      id: string;
      name: string;
      label: string;
      type?: string;
      placeholder?: string;
      required?: boolean;
   }>;
   
   // API functions
   validateCredentials: (data: any) => Promise<{ valid: boolean; message: string }>;
   connectIntegration: (data: any) => Promise<void>;
   encryptCredentials?: (data: any) => Promise<any>;
   
   // Optional customization
   title?: string;
   description?: string;
   redirectPath?: string;
   currentStep?: number;
   completedSteps?: number[];
}

/**
 * Generic Integration Template Component
 * 
 * This component provides a standardized way to create integration forms
 * with consistent UI/UX across all integrations.
 * 
 * Usage Example:
 * 
 * ```tsx
 * <IntegrationTemplate
 *    integrationName="MyService"
 *    logo={myServiceLogo}
 *    steps={myServiceSteps}
 *    formFields={[
 *       { id: 'username', name: 'username', label: 'Username', required: true },
 *       { id: 'password', name: 'password', label: 'Password', type: 'password', required: true }
 *    ]}
 *    validateCredentials={validateMyServiceCredentials}
 *    connectIntegration={connectToMyService}
 * />
 * ```
 */
const IntegrationTemplate: React.FC<IntegrationTemplateProps> = ({
   integrationName,
   logo,
   steps,
   formFields,
   validateCredentials,
   connectIntegration,
   encryptCredentials,
   title,
   description,
   redirectPath = '/social-listening',
   currentStep = 0,
   completedSteps = [],
}) => {
   // Get client_id from localStorage
   const { client_id } = LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   // Create initial form data from form fields
   const initialFormData = formFields.reduce((acc, field) => {
      acc[field.name] = '';
      return acc;
   }, {} as Record<string, string>);

   // Use the integration hook
   const {
      formData,
      isLoading,
      apiError,
      handleInputChange,
      handleSubmit,
   } = useIntegration({
      initialFormData,
      validateCredentials,
      connectIntegration,
      encryptCredentials,
      redirectPath,
      integrationName,
   });

   const getErrorMessage = () => {
      if (apiError && !apiError.success) {
         return apiError.message;
      }
      return null;
   };

   const getSuccessMessage = () => {
      if (apiError && apiError.success) {
         return apiError.message;
      }
      return null;
   };

   const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      if (client_id) {
         handleSubmit(e, client_id);
      }
   };

   return (
      <IntegrationWrapper
         title={integrationName}
         logo={logo}
         logoAlt={`${integrationName} Logo`}
         steps={steps}
         currentStep={currentStep}
         completedSteps={completedSteps}
      >
         <IntegrationForm
            title={title || `${integrationName} Integration`}
            description={description || `Please provide your ${integrationName} credentials to connect your account.`}
            onSubmit={onSubmit}
            isLoading={isLoading}
            error={getErrorMessage()}
            success={getSuccessMessage()}
            submitButtonText="Connect"
         >
            {formFields.map((field) => (
               <IntegrationInput
                  key={field.id}
                  id={field.id}
                  name={field.name}
                  label={field.label}
                  type={field.type || 'text'}
                  placeholder={field.placeholder || `Enter your ${field.label.toLowerCase()}...`}
                  value={formData[field.name] || ''}
                  onChange={handleInputChange}
                  required={field.required}
               />
            ))}
         </IntegrationForm>
      </IntegrationWrapper>
   );
};

export default IntegrationTemplate;
